import { IconType } from "react-icons";
import { ReactNode } from "react";

export type BlindType = "blinds" | "control-system";
export interface BlindTypeMain {
  title: string;
  description: string;
  image: string;
  buttonText: string;
  slug: string;
  type: "blinds" | "control-system";
}

export interface Blind {
  title: string;
  description: string;
  image: string;
  buttonText: string;
  slug: string;
  type: BlindType;
}

export interface CompatibleBlindsData {
  heading: string;
  subHeading: string;
  blinds: Blind[];
}

export interface ControlSystemData {
  slug: string;
  title: string;
  icon: string;
  description: string;
  image: string;
  controlSystemBanner: {
    heading: string;
    subHeading: string;
    buttonText: string;
    coverImage: string;
  };
  whyChooseControlSlider: {
    heading: string;
    subHeading: string;
    features: {
      icon: ReactNode;
      title: string;
      image: string;
    }[];
  };
  productionSteps: {
    heading: string;
    subHeading: string;
    features: {
      icon: IconType;
      title: string;
      description: string;
    }[];
  };
  compatibleBlinds: CompatibleBlindsData;
  howItWorks: {
    heading: string;
    subHeading: string;
    steps: {
      icon: ReactNode;
      title: string;
      description: string;
      isFilled: boolean;
      image: string;
    }[];
  };
  safetyMaintenance: {
    heading: string;
    subHeading: string;
    features: {
      title: string;
      description: string;
      image: string;
    }[];
  };
  quoteCTASection: {
    heading: string;
    subHeading: string;
    buttonText: string;
    background: string;
  };
}
