"use client";
import React, { useRef } from "react";
import { rubik } from "@/fonts";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation, Pagination } from "swiper/modules";
import { CiCircleChevLeft, CiCircleChevRight } from "react-icons/ci";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import BlindCard from "@/components/common/cards/BlindCard";
import { BlindTypeMain } from "@/types/control-system";

type Props = {
  data: BlindTypeMain[];
  heading1?: string;
  heading2?: string;
};

const BlindsShowcaseLanding: React.FC<Props> = ({
  data,
  heading1,
  heading2,
}) => {
  const swiperRef = useRef<any>(null);

  const handlePrevSlide = () => {
    if (swiperRef.current) {
      swiperRef.current.slidePrev();
    }
  };

  const handleNextSlide = () => {
    if (swiperRef.current) {
      swiperRef.current.slideNext();
    }
  };

  const blindsWithDefaults = data.map((blind) => ({
    ...blind,
    slug: blind.slug || blind.title.toLowerCase().replace(/\s+/g, "-"),
    type: blind.type || "blinds",
  }));

  return (
    <div className="py-16">
      <div className="mx-auto max-w-[1500px] px-3 md:px-5">
        {heading1 && (
          <div className="mb-14 flex justify-center">
            <h2
              className={`${rubik.className} z-0 mb-10 text-center text-3xl font-semibold text-[#013F68] sm:text-4xl md:text-5xl`}
            >
              {heading1}
            </h2>
            <h2
              className={`${rubik.className} z-0 mb-10 ml-[10px] text-center text-3xl font-semibold text-[#FFA600] sm:text-4xl md:text-5xl`}
            >
              {heading2}
            </h2>
          </div>
        )}
        <div className="relative mb-8 flex items-center gap-3 sm:gap-6 md:mb-16 md:gap-9 lg:gap-12 xl:gap-16">
          <button
            className="text-3xl text-[#FFA600] sm:text-4xl md:text-5xl lg:text-6xl"
            onClick={handlePrevSlide}
          >
            <CiCircleChevLeft />
          </button>
          <Swiper
            onSwiper={(swiper) => {
              swiperRef.current = swiper;
            }}
            modules={[Navigation, Pagination]}
            spaceBetween={16}
            slidesPerView={3}
            pagination={{
              clickable: true,
              dynamicBullets: true,
            }}
            breakpoints={{
              1: {
                slidesPerView: 1,
              },
              800: {
                slidesPerView: 2,
              },
              1000: {
                slidesPerView: 3,
              },
            }}
            className="w-full"
          >
            {blindsWithDefaults.map((blind, index) => (
              <SwiperSlide key={index}>
                <BlindCard {...blind} isLp={true} />
              </SwiperSlide>
            ))}
          </Swiper>
          <button
            className="text-3xl text-[#FFA600] sm:text-4xl md:text-5xl lg:text-6xl"
            onClick={handleNextSlide}
          >
            <CiCircleChevRight />
          </button>
        </div>
      </div>
    </div>
  );
};

export default BlindsShowcaseLanding;
