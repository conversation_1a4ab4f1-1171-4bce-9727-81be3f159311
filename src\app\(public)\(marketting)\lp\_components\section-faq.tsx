"use client";

import { useState } from "react";
import { ChevronDownIcon } from "lucide-react";
import { pageData } from "../config";

export function SectionFAQ() {
  const { faq } = pageData;
  const [openQuestion, setOpenQuestion] = useState<number | null>(1);

  const toggleQuestion = (questionId: number) => {
    setOpenQuestion(openQuestion === questionId ? null : questionId);
  };

  return (
    <section className="w-full bg-gray-50 py-16 lg:py-24">
      <div className="mx-auto max-w-[1470px] px-4">
        <div className="flex flex-col items-center justify-center gap-10 min-[1100px]:flex-row min-[1100px]:gap-32">
          {/* Left side - Title */}
          <div className="w-full space-y-6 min-[1100px]:w-1/2">
            <div className="space-y-4">
              <p className="text-xl font-bold uppercase tracking-wide text-[#013F68]">
                {faq.description}
              </p>
              <div className="space-y-2">
                <h2 className="text-6xl font-black leading-tight">
                  <span className="text-[#FFA600]">{faq.title}</span>
                </h2>
                <h2 className="text-6xl font-black leading-tight">
                  <span
                    className="text-transparent"
                    style={{
                      WebkitTextStroke: "2px #013F68",
                    }}
                  >
                    {faq.subtitle}
                  </span>
                </h2>
                <h2 className="text-6xl font-black leading-tight">
                  <span className="text-[#FFA600]">{faq.highlight}</span>
                </h2>
              </div>
            </div>
          </div>

          {/* Right side - FAQ Items */}
          <div className="w-full space-y-4 min-[1100px]:w-1/2">
            {faq.questions.map((item) => (
              <div key={item.id} className="w-full">
                <button
                  onClick={() => toggleQuestion(item.id)}
                  className="flex w-full items-center justify-between rounded-full border-2 border-[#013F68] bg-gradient-to-r from-[#FFA600] to-[#FF8C00] px-8 py-5 text-left shadow-lg transition-all duration-300 hover:shadow-xl"
                >
                  <span className="text-lg font-bold text-[#013F68]">
                    {item.id}. {item.question}
                  </span>
                  <ChevronDownIcon
                    className={`ml-4 h-6 w-6 flex-shrink-0 text-[#013F68] transition-transform duration-300 ${
                      openQuestion === item.id ? "rotate-180" : ""
                    }`}
                  />
                </button>
                <div
                  className={`overflow-hidden transition-all duration-300 ${
                    openQuestion === item.id
                      ? "max-h-96 opacity-100"
                      : "max-h-0 opacity-0"
                  }`}
                >
                  <div className="mt-3 rounded-3xl border-2 border-[#013F68] bg-white p-6">
                    <p className="text-base leading-relaxed text-[#013F68]">
                      {item.answer}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
