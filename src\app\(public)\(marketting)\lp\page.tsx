import WarrantyInfoSection from "@/components/common/WarrantyInfoSection";
import { blinds } from "../../(web)/blinds/data";
import BlindsShowcaseLanding from "./_components/blinds-catalogue";
import { SectionHero } from "./_components/section-hero";
import { contactPage } from "@/configs/pages-data/contact";
import LeadingCalagry from "./_components/leading-calagry";
import { SectionFAQ } from "./_components/section-faq";
import { SectionContact } from "./_components/section-contact";

export default function LPPage() {
  return (
    <main>
      <SectionHero />
      <BlindsShowcaseLanding
        data={blinds}
        heading1="BLINDS"
        heading2="CATALOGUE"
      />
      <WarrantyInfoSection data={contactPage.warrantyInfoSection} isLp={true} />
      <LeadingCalagry />
      <SectionFAQ />
      <SectionContact />
    </main>
  );
}
