"use client";

import React from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";

const BlindCard: React.FC<{
  title: string;
  isLp?: boolean;
  description: string;
  buttonText: string;
  image: string;
  slug: string;
  type: "blinds" | "control-system";
}> = ({ title, image, description, buttonText, slug, type, isLp }) => {
  const router = useRouter();

  return (
    <div className="relative flex min-h-[500px] flex-col items-stretch justify-end gap-3 overflow-hidden rounded p-5 sm:p-10">
      {/* ✅ Background Image */}
      <Image
        src={image}
        alt={title}
        fill
        quality={100}
        priority
        style={{
          objectFit: "cover",
          objectPosition: "center",
        }}
        className="z-[-2]"
      />

      {/* ✅ Gradient overlay */}
      <div className="absolute inset-0 z-[-1] bg-gradient-to-b from-[#f7f9fa00] via-[#132D59cc] to-[#132D59]" />

      {/* ✅ Foreground Content */}
      <h4 className="text-3xl font-bold text-white">{title}</h4>
      <p className="text-white">{description}</p>
      <div>
        {isLp ? null : (
          <button
            className="rounded-full bg-[#FFA600] px-5 py-2 text-white"
            onClick={() => router.push(`/${type}/${slug}`)}
          >
            <span>{buttonText}</span>
          </button>
        )}
      </div>
    </div>
  );
};

export default BlindCard;
