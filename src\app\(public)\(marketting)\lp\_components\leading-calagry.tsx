import Image from "next/image";
import React from "react";
import Link from "next/link";

function LeadingCalgary() {
  return (
    <div
      className="w-full px-4 py-12 text-white sm:px-6 lg:px-8"
      style={{
        background: "url(/images/lp/sction-bg.png) no-repeat center center",
        backgroundSize: "cover",
      }}
    >
      <div className="mx-auto flex max-w-7xl flex-col-reverse items-center justify-between gap-10 md:flex-row">
        {/* LEFT CONTENT */}
        <div className="w-full space-y-6 text-center md:w-1/2 md:text-left">
          <h2 className="text-3xl font-extrabold leading-tight text-white sm:text-4xl md:text-5xl">
            LEADING <span className="text-[#FFA600]">CALGARY</span>{" "}
            <span className="text-[#FFA600]">BLINDS</span> COMPANY
          </h2>
          <p className="text-sm leading-relaxed text-white sm:text-base md:text-lg">
            <PERSON> Blinds, Your Trusted Canadian Blinds In Calgary, Alberta, Takes
            Pride In Transforming Your Living Space With High Quality Window
            Coverings That Seamlessly Marry Style and Functionality.
          </p>
          <div className="mt-4">
            <Link href="/shop">
              <button className="rounded-full bg-[#FFA600] px-6 py-3 text-sm font-semibold text-white transition hover:bg-[#e69500]">
                Shop Sales →
              </button>
            </Link>
          </div>
        </div>

        {/* RIGHT IMAGE */}
        <div className="relative h-[300px] w-[300px] sm:h-[350px] sm:w-[350px] md:h-[450px] md:w-[450px]">
          <Image
            src="/images/lp/blinds-calagry.png"
            alt="Calgary Blinds"
            layout="fill"
            objectFit="contain"
            priority
          />
        </div>
      </div>
    </div>
  );
}

export default LeadingCalgary;
