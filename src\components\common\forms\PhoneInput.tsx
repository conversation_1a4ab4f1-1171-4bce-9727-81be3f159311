"use client";

import { useState, useEffect, ChangeEvent } from "react";
import { getCountryList } from "./getCountryList";

interface Country {
  name: string;
  dialCode: string;
  iso2: string;
  flagUrl: string;
}

interface PhoneInputProps {
  label?: string;
  placeholder?: string;
  value?: string;
  onChange?: (fullNumber: string) => void;
  containerClass?: string;
  isSmall?: boolean;
}

export default function PhoneInput({
  label = "Phone",
  placeholder = "Phone number...",
  value = "",
  onChange = () => {},
  containerClass = "",
  isSmall = false,
}: PhoneInputProps) {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [countries, setCountries] = useState<Country[]>([]);
  const [filteredCountries, setFilteredCountries] = useState<Country[]>([]);
  const [selectedCountry, setSelectedCountry] = useState<Country | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [number, setNumber] = useState("");

  useEffect(() => {
    const all = getCountryList();
    setCountries(all);
    setFilteredCountries(all);

    const canada = all.find((c) => c.dialCode === "+1" && c.name === "Canada");
    if (canada) setSelectedCountry(canada);
  }, []);

  useEffect(() => {
    if (value && countries.length > 0) {
      const parts = value.trim().split(" ");
      const dialCode = parts[0];
      const num = parts.slice(1).join(" ");
      const match = countries.find((c) => c.dialCode === dialCode);
      if (match) setSelectedCountry(match);
      setNumber(num);
    }
  }, [value, countries]);

  useEffect(() => {
    const filtered = countries.filter((country) =>
      `${country.name} ${country.dialCode}`
        .toLowerCase()
        .includes(searchTerm.toLowerCase()),
    );
    setFilteredCountries(filtered);
  }, [searchTerm, countries]);

  const handleChange = (num: string) => {
    setNumber(num);
    if (selectedCountry) {
      onChange(`${selectedCountry.dialCode} ${num}`);
    } else {
      onChange(num);
    }
  };

  const handleSelect = (country: Country) => {
    setSelectedCountry(country);
    setIsDropdownOpen(false);
    setSearchTerm("");
    onChange(`${country.dialCode} ${number}`);
  };

  return (
    <div className={`flex w-full flex-col ${containerClass}`}>
      <label className="text-second-black inter mb-2 text-sm">{label}</label>
      <div
        className={`relative flex w-full items-center rounded-[23px] bg-[#EFEFEF] ${
          isSmall ? "p-3 text-xs" : "p-4 text-sm"
        }`}
      >
        <button
          type="button"
          className="mr-2 flex w-[100px] items-center gap-2 border-r border-gray-300 pr-2"
          onClick={() => setIsDropdownOpen(!isDropdownOpen)}
        >
          <div className="flex w-full items-center justify-between">
            {selectedCountry && (
              <>
                <img
                  src={selectedCountry.flagUrl}
                  alt={selectedCountry.name}
                  className="h-4 w-5 object-cover"
                />
                <span>{selectedCountry.dialCode}</span>
              </>
            )}
            <svg
              className="ml-1 h-[20px] w-[20px] text-gray-500"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M19 9l-7 7-7-7"
              />
            </svg>
          </div>
        </button>

        <input
          type="number"
          placeholder={placeholder}
          value={number}
          onChange={(e: ChangeEvent<HTMLInputElement>) =>
            handleChange(e.target.value)
          }
          className="w-[100%] bg-transparent outline-none"
        />

        {/* Dropdown */}
        {isDropdownOpen && (
          <div className="absolute left-0 top-full z-[9999] mt-2 max-h-[300px] w-full overflow-y-auto rounded-md border border-gray-300 bg-white shadow-lg">
            <div className="border-b p-2">
              <input
                type="text"
                placeholder="Search country"
                className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            {filteredCountries.map((country) => (
              <button
                key={country.iso2}
                className="flex w-full items-center gap-2 px-4 py-2 text-sm hover:bg-gray-100"
                onClick={() => handleSelect(country)}
              >
                <img
                  src={country.flagUrl}
                  alt={country.name}
                  className="h-4 w-5"
                />
                {country.name} ({country.dialCode})
              </button>
            ))}
            {filteredCountries.length === 0 && (
              <div className="p-4 text-center text-sm text-gray-500">
                No countries found
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
