import { ArrowRightIcon } from "lucide-react";
import { pageData } from "../config";
import LPContactForm from "./lp-contact-form";

export function SectionHero() {
  const { hero } = pageData;

  return (
    <section
      className="glob min-h-[calc(100vh-9rem)] w-full bg-cover bg-center bg-no-repeat py-20" // CHANGED: Standardized vertical padding
      style={{ backgroundImage: `url('${hero.backgroundImage}')` }}
    >
      <div className="global-container mx-auto flex h-full w-full items-center justify-center gap-4">
        {" "}
        <div className="h-full w-1/2 space-y-6 text-white">
          <div>
            <h4 className="text-2xl font-medium">{hero.specialOffer.badge}</h4>
            <h2
              className="max-w-xl text-7xl font-bold"
              style={{ color: "#FFA600" }}
            >
              {hero.specialOffer.title}
            </h2>
          </div>
          <ul className="list-inside list-disc space-y-2 text-lg marker:text-[#FFA600]">
            {hero.benefits.map((benefit, index) => (
              <li key={index}>{benefit}</li>
            ))}
          </ul>
          <p className="text-3xl font-medium">
            <span className="line-through">{hero.pricing.originalPrice}</span>{" "}
            <span style={{ color: "#FFA600" }}>
              {hero.pricing.discountedPrice}
            </span>
          </p>
          <p className="max-w-lg text-xl">{hero.description}</p>
          <button
            className="flex items-center gap-2 rounded-3xl px-4 py-2 font-bold"
            style={{
              backgroundColor: "#FFA600",
              color: "#013F68",
            }}
          >
            {hero.ctaButton.text} <ArrowRightIcon />
          </button>
          <div className="flex items-start">
            <img
              width={224}
              src={hero.certificates[0].src}
              alt={hero.certificates[0].alt}
            />
            <img
              width={140}
              src={hero.certificates[1].src}
              alt={hero.certificates[1].alt}
            />
          </div>
        </div>
        <div className="flex h-full w-1/2 items-center justify-end">
          <div className="relative h-full w-full space-y-6 rounded-3xl border-2 border-white bg-black/45 p-6 lg:w-4/6">
            <img
              className="absolute right-4 top-0"
              width={108}
              height={124}
              src={hero.formSection.award.src}
              alt={hero.formSection.award.alt}
            />
            <h3 className="max-w-xs text-4xl font-semibold text-white">
              {hero.formSection.title}
            </h3>
            <LPContactForm />
          </div>
        </div>
      </div>
    </section>
  );
}
