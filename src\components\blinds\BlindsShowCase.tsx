import React from "react";
import BlindCard from "../common/cards/BlindCard";
import { BlindTypeMain } from "@/types/control-system";

interface BlindsShowCaseProps {
  blinds: BlindTypeMain[];
  heading: string;
  isSlider?: boolean;
}

function BlindsShowCase({ blinds, heading }: BlindsShowCaseProps) {
  return (
    <div className="py-16">
      <div className="mx-auto max-w-[1500px] px-3 md:px-5">
        {heading && (
          <div className="mb-14 flex justify-center">
            <h2 className="text-center text-3xl font-semibold text-[#013F68] sm:text-4xl md:text-5xl">
              {heading}
            </h2>
          </div>
        )}
        <div className="grid grid-cols-1 gap-3 md:grid-cols-2 lg:grid-cols-3">
          {blinds.map((blind, index) => (
            <BlindCard key={index} {...blind} />
          ))}
        </div>
      </div>
    </div>
  );
}

export default BlindsShowCase;
