import { pageData } from "../config";
import { websiteInfo } from "@/configs/info";
import LPContactForm from "./lp-contact-form";

export function SectionContact() {
  const { contact } = pageData;

  return (
    <section
      className="min-h-[calc(100vh-9rem)] w-full bg-cover bg-center bg-no-repeat py-16 lg:py-24"
      style={{ backgroundImage: `url('${contact.backgroundImage}')` }}
    >
      <div className="mx-auto max-w-[1470px] px-4">
        <div className="flex h-full items-center justify-center gap-10 min-[1100px]:gap-32">
          <div className="h-full w-1/2 space-y-6 text-white">
          <div className="space-y-2">
            <h2 className="max-w-xl text-7xl font-bold">
              {contact.mainHeading.text}{" "}
              <span style={{ color: "#FFA600" }}>
                {contact.mainHeading.highlightText}
              </span>{" "}
              {contact.mainHeading.continuationText}{" "}
              <span style={{ color: "#FFA600" }}>
                {contact.mainHeading.finalHighlight}
              </span>
            </h2>
          </div>

          <div className="space-y-3">
            <div className="inline-block rounded-full bg-[#FFA600] px-4 py-1">
              <span className="text-sm font-medium text-black">
                {contact.location.badge}
              </span>
            </div>
            <div className="flex items-center gap-4">
              <img
                width={192}
                src={contact.pictures[0].src}
                alt={contact.pictures[0].alt}
              />
              <img
                width={264}
                src={contact.pictures[1].src}
                alt={contact.pictures[1].alt}
              />
            </div>
            <p className="text-lg text-white/90">{contact.location.address}</p>
          </div>

          <div className="space-y-3">
            <div className="inline-block rounded-full bg-[#FFA600] px-4 py-1">
              <span className="text-sm font-medium text-black">
                {contact.contactInfo.badge}
              </span>
            </div>
            <div className="space-y-2 text-white/90">
              <div>
                <p className="font-medium">{contact.contactInfo.hours.label}</p>
                <p>{contact.contactInfo.hours.weekdays}</p>
                <p>{contact.contactInfo.hours.weekends}</p>
              </div>
              <div className="space-y-1">
                <p>
                  <span className="font-medium">Phone:</span>{" "}
                  {websiteInfo.phoneNumber.display}
                </p>
                <p>
                  <span className="font-medium">Toll-free:</span>{" "}
                  {websiteInfo.tollFreeNumber.display}
                </p>
                <p>{websiteInfo.email.display}</p>
              </div>
            </div>
          </div>
          <div className="flex h-full w-1/2 items-center justify-end">
            <div className="relative h-full w-full space-y-6 rounded-3xl border-2 border-white bg-black/45 p-6 lg:w-4/6">
              <img
                className="absolute right-4 top-0"
                width={108}
                height={124}
                src={contact.formSection.award.src}
                alt={contact.formSection.award.alt}
              />
              <div className="space-y-2">
                <h3 className="text-4xl font-semibold text-white">
                  {contact.formSection.title}
                </h3>
                <p className="max-w-xs text-sm text-white/80">
                  {contact.formSection.subtitle}
                </p>
              </div>
              <LPContactForm />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
