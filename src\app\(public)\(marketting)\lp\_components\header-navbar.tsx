"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";

import { scrollToTop } from "@/hooks/useScroll";
import { websiteInfo } from "@/configs/info";
import { MailIcon, PhoneCallIcon, VoicemailIcon } from "lucide-react";
import { pageData } from "../config";

export function HeaderNavbar() {
  const { navbar } = pageData;

  const navLinks = [
    {
      label: websiteInfo.phoneNumber.display,
      link: `tel:${websiteInfo.phoneNumber.link}`,
      Icon: PhoneCallIcon,
    },
    {
      label: websiteInfo.tollFreeNumber.display,
      link: `tel:${websiteInfo.tollFreeNumber.link}`,
      Icon: VoicemailIcon,
    },
    {
      label: websiteInfo.email.display,
      link: `mailto:${websiteInfo.email.link}`,
      Icon: MailIcon,
    },
  ];

  const pathname = usePathname();

  const handleLogoClick = (e: React.MouseEvent) => {
    if (pathname === "/") {
      e.preventDefault();
      scrollToTop();
    }
  };

  return (
    <div className="bg-white">
      <div className="global-container flex items-center justify-between py-4">
        <div className="flex items-center">
          <Link href="/" onClick={handleLogoClick} prefetch>
            <img
              className="ml-2 pt-2"
              width={200}
              height={50}
              src={websiteInfo.logo.src}
              alt={websiteInfo.logo.alt}
            />
          </Link>
        </div>
        <ul className="flex space-x-8">
          {navLinks.map(({ label, link, Icon }) => (
            <li key={label}>
              <Link href={link} className="flex items-center space-x-2">
                <Icon className="size-5 text-[#FFA600]" />
                <span className="text-[#013F68]">{label}</span>
              </Link>
            </li>
          ))}
        </ul>
        <div>
          <Link
            href={navbar.contactButton.href}
            className="rounded-xl bg-[#FFA600] px-4 py-2 font-bold text-[#013F68]"
          >
            {navbar.contactButton.text}
          </Link>
        </div>
      </div>
    </div>
  );
}
